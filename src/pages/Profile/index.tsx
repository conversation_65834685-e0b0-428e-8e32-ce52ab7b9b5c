import React, {useEffect, useState} from 'react';
import {Card, List, Modal, Toast} from 'antd-mobile';
import {
  CreditCardOutlined,
  EditOutlined,
  GlobalOutlined,
  LockOutlined,
  LogoutOutlined,
  MessageOutlined,
  UserOutlined,
  WalletOutlined,
  HistoryOutlined,
  BankOutlined,
  PlusOutlined,
  RightOutlined,
  FileTextOutlined,
  AccountBookOutlined
} from '@ant-design/icons';
import {useNavigate} from 'react-router-dom';
import Layout from '../../components/Layout';
import type {UserInfo} from '../../services/auth';
import {getUserInfo, logout} from '../../services/auth';
import './index.css';

const Profile: React.FC = () => {
  const [userInfo, setUserInfo] = useState<UserInfo | null>(null);
  const [loading, setLoading] = useState(true);
  const navigate = useNavigate();

  useEffect(() => {
    loadUserInfo();
  }, []);

  const loadUserInfo = async () => {
    try {
      const token = localStorage.getItem('token');
      if (!token) {
        navigate('/login');
        return;
      }

      const response = await getUserInfo();
      if (response && response.code === 200) {
        setUserInfo(response.data);
      } else {
        Toast.show('获取用户信息失败');
      }
    } catch (error) {
      console.error('获取用户信息失败:', error);
      Toast.show('获取用户信息失败');
    } finally {
      setLoading(false);
    }
  };

  const handleLogout = async () => {
    Modal.confirm({
      title: '确认登出',
      content: '您确定要登出吗？',
      onConfirm: async () => {
        try {
          await logout();
          localStorage.removeItem('token');
          Toast.show('登出成功');
          navigate('/login');
        } catch (error) {
          console.error('登出失败:', error);
          // 即使登出接口失败，也清除本地token
          localStorage.removeItem('token');
          navigate('/login');
        }
      },
    });
  };

  // 功能菜单项
  const menuItems = [
    {
      id: 1,
      title: 'Withdraw',
      icon: 'WalletOutlined',
      color: '#10b981',
      action: () => Toast.show('提现功能开发中')
    },
    {
      id: 2,
      title: 'Recharge Details',
      icon: 'HistoryOutlined',
      color: '#3b82f6',
      action: () => Toast.show('充值明细功能开发中')
    },
    {
      id: 3,
      title: 'Withdrawal Details',
      icon: 'FileTextOutlined',
      color: '#f59e0b',
      action: () => Toast.show('提现明细功能开发中')
    },
    {
      id: 4,
      title: 'Account Details',
      icon: 'AccountBookOutlined',
      color: '#8b5cf6',
      action: () => Toast.show('账户详情功能开发中')
    },
    {
      id: 5,
      title: 'Message',
      icon: 'MessageOutlined',
      color: '#06b6d4',
      action: () => Toast.show('消息功能开发中')
    },
    {
      id: 6,
      title: 'Bind Bank Card',
      icon: 'BankOutlined',
      color: '#ec4899',
      action: () => Toast.show('绑定银行卡功能开发中')
    },
    {
      id: 7,
      title: 'Change Password',
      icon: 'LockOutlined',
      color: '#f97316',
      action: () => Toast.show('修改密码功能开发中')
    },
    {
      id: 8,
      title: 'Exit',
      icon: 'LogoutOutlined',
      color: '#ef4444',
      action: handleLogout
    }
  ];



  // 事件处理函数
  const handleMenuClick = (item: any) => {
    if (item.action) {
      item.action();
    }
  };

  // 渲染图标的函数
  const renderIcon = (iconName: string, style?: React.CSSProperties) => {
    const iconProps = {
      style: { fontSize: '20px', ...style }
    };

    switch (iconName) {
      case 'WalletOutlined':
        return <WalletOutlined {...iconProps} />;
      case 'HistoryOutlined':
        return <HistoryOutlined {...iconProps} />;
      case 'FileTextOutlined':
        return <FileTextOutlined {...iconProps} />;
      case 'AccountBookOutlined':
        return <AccountBookOutlined {...iconProps} />;
      case 'MessageOutlined':
        return <MessageOutlined {...iconProps} />;
      case 'BankOutlined':
        return <BankOutlined {...iconProps} />;
      case 'LockOutlined':
        return <LockOutlined {...iconProps} />;
      case 'GlobalOutlined':
        return <GlobalOutlined {...iconProps} />;
      case 'LogoutOutlined':
        return <LogoutOutlined {...iconProps} />;
      case 'PlusOutlined':
        return <PlusOutlined {...iconProps} />;
      default:
        return null;
    }
  };

  const getGenderText = (gender?: number) => {
    switch (gender) {
      case 1:
        return '男';
      case 2:
        return '女';
      default:
        return '未知';
    }
  };

  if (loading) {
    return (
      <Layout>
        <div className="loading">加载中...</div>
      </Layout>
    );
  }

  if (!userInfo) {
    return (
      <Layout>
        <div className="error">用户信息加载失败</div>
      </Layout>
    );
  }

  return (
    <Layout>
      <div className="profile-page">
        <div className="profile-content">

        {/* 账户信息卡片 */}
        <div className="profile-header">
          <Card className="account-card">
            {/* 顶部用户信息 */}
            <div className="account-header">
              <div className="user-avatar">
                {userInfo.avatar ? (
                  <img
                    src={userInfo.avatar}
                    alt="Avatar"
                    className="avatar-image"
                  />
                ) : (
                  <UserOutlined className="avatar-icon" />
                )}
              </div>
              <div className="user-basic-info">
                <h2 className="username">{userInfo.username}</h2>
                <div className="extension-code">
                  Extension Code: <span className="code-highlight">{userInfo.inviteCode}</span>
                </div>
              </div>
            </div>

            {/* 余额信息 */}
            <div className="balance-section">
              <div className="total-balance">
                <div className="balance-label">总余额</div>
                <div className="balance-amount">
                  <span className="amount-value">{userInfo.balance?.toFixed(4) || '0.0000'}</span>
                  <button className="add-money-btn" onClick={() => Toast.show('充值功能开发中')}>
                    {renderIcon('PlusOutlined', { fontSize: '16px', color: '#fff' })}
                  </button>
                </div>
              </div>

              {/* 余额明细 */}
              <div className="balance-details">
                <div className="balance-item available">
                  <div className="balance-item-label">Available balance</div>
                  <div className="balance-item-value available-amount">
                    {userInfo.balance?.toFixed(2) || '0.00'}
                  </div>
                </div>
                <div className="balance-item frozen">
                  <div className="balance-item-label">Frozen amount</div>
                  <div className="balance-item-value frozen-amount">
                    {userInfo.frozenBalance?.toFixed(2) || '0.00'}
                  </div>
                </div>
              </div>
            </div>
          </Card>
        </div>

        {/* 功能菜单列表 */}
        <div className="menu-section">
          <Card className="menu-card">
            <List className="function-menu">
              {menuItems.map((item) => (
                <List.Item
                  key={item.id}
                  prefix={
                    <div className="menu-icon-wrapper">
                      {renderIcon(item.icon, { fontSize: '20px', color: item.color })}
                    </div>
                  }
                  extra={
                    <RightOutlined style={{ fontSize: '14px', color: '#999' }} />
                  }
                  onClick={() => handleMenuClick(item)}
                  className={`menu-item ${item.id === 8 ? 'exit-item' : ''}`}
                  arrow={false}
                >
                  <div className="menu-content">
                    <div className="menu-title">{item.title}</div>
                  </div>
                </List.Item>
              ))}
            </List>
          </Card>
        </div>

        </div>
      </div>
    </Layout>
  );
};

export default Profile;
