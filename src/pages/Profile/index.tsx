import React, {useEffect, useState} from 'react';
import {Card, List, Modal, Toast} from 'antd-mobile';
import {
  CreditCardOutlined,
  EditOutlined,
  GlobalOutlined,
  LockOutlined,
  LogoutOutlined,
  MessageOutlined,
  UserOutlined,
  WalletOutlined,
  HistoryOutlined,
  TeamOutlined,
  SettingOutlined,
  QuestionCircleOutlined,
  SafetyOutlined
} from '@ant-design/icons';
import {useNavigate} from 'react-router-dom';
import Layout from '../../components/Layout';
import type {UserInfo} from '../../services/auth';
import {getUserInfo, logout} from '../../services/auth';
import './index.css';

const Profile: React.FC = () => {
  const [userInfo, setUserInfo] = useState<UserInfo | null>(null);
  const [loading, setLoading] = useState(true);
  const navigate = useNavigate();

  useEffect(() => {
    loadUserInfo();
  }, []);

  const loadUserInfo = async () => {
    try {
      const token = localStorage.getItem('token');
      if (!token) {
        navigate('/login');
        return;
      }

      const response = await getUserInfo();
      if (response && response.code === 200) {
        setUserInfo(response.data);
      } else {
        Toast.show('获取用户信息失败');
      }
    } catch (error) {
      console.error('获取用户信息失败:', error);
      Toast.show('获取用户信息失败');
    } finally {
      setLoading(false);
    }
  };

  const handleLogout = async () => {
    Modal.confirm({
      title: '确认登出',
      content: '您确定要登出吗？',
      onConfirm: async () => {
        try {
          await logout();
          localStorage.removeItem('token');
          Toast.show('登出成功');
          navigate('/login');
        } catch (error) {
          console.error('登出失败:', error);
          // 即使登出接口失败，也清除本地token
          localStorage.removeItem('token');
          navigate('/login');
        }
      },
    });
  };

  // 功能菜单项
  const menuItems = [
    {
      id: 1,
      title: '编辑个人信息',
      icon: 'EditOutlined',
      color: '#10b981',
      action: () => navigate('/edit-profile')
    },
    {
      id: 2,
      title: '修改密码',
      icon: 'LockOutlined',
      color: '#3b82f6',
      action: () => Toast.show('修改密码功能开发中')
    },
    {
      id: 3,
      title: '设置支付密码',
      icon: 'CreditCardOutlined',
      color: '#f59e0b',
      action: () => Toast.show('支付密码功能开发中')
    },
    {
      id: 4,
      title: '消息中心',
      icon: 'MessageOutlined',
      color: '#8b5cf6',
      action: () => Toast.show('消息中心功能开发中')
    },
    {
      id: 5,
      title: '语言设置',
      icon: 'GlobalOutlined',
      color: '#ef4444',
      action: () => Toast.show('语言设置功能开发中')
    },
    {
      id: 6,
      title: '退出登录',
      icon: 'LogoutOutlined',
      color: '#dc2626',
      action: handleLogout
    }
  ];



  // 事件处理函数
  const handleMenuClick = (item: any) => {
    if (item.action) {
      item.action();
    }
  };

  // 渲染图标的函数
  const renderIcon = (iconName: string, style?: React.CSSProperties) => {
    const iconProps = {
      style: { fontSize: '20px', ...style }
    };

    switch (iconName) {
      case 'EditOutlined':
        return <EditOutlined {...iconProps} />;
      case 'LockOutlined':
        return <LockOutlined {...iconProps} />;
      case 'CreditCardOutlined':
        return <CreditCardOutlined {...iconProps} />;
      case 'MessageOutlined':
        return <MessageOutlined {...iconProps} />;
      case 'GlobalOutlined':
        return <GlobalOutlined {...iconProps} />;
      case 'LogoutOutlined':
        return <LogoutOutlined {...iconProps} />;
      default:
        return null;
    }
  };

  const getGenderText = (gender?: number) => {
    switch (gender) {
      case 1:
        return '男';
      case 2:
        return '女';
      default:
        return '未知';
    }
  };

  if (loading) {
    return (
      <Layout>
        <div className="loading">加载中...</div>
      </Layout>
    );
  }

  if (!userInfo) {
    return (
      <Layout>
        <div className="error">用户信息加载失败</div>
      </Layout>
    );
  }

  return (
    <Layout>
      <div className="profile-page">
        <div className="profile-content">

        {/* 个人信息模块 */}
        <div className="profile-header">
          <Card className="user-card">
            {/* 用户基本信息 */}
            <div className="user-info">
              <div className="user-avatar">
                {userInfo.avatar ? (
                  <img
                    src={userInfo.avatar}
                    alt="Avatar"
                    className="avatar-image"
                  />
                ) : (
                  <UserOutlined className="avatar-icon" />
                )}
              </div>
              <div className="user-details">
                <h2 className="username">{userInfo.nickname || userInfo.username}</h2>
                <div className="user-code">
                  邀请码: <span className="code-text">{userInfo.inviteCode}</span>
                </div>
                <div className="user-meta">
                  <span>用户名: {userInfo.username}</span>
                  {userInfo.phone && <span> | 手机: {userInfo.phone}</span>}
                  <span> | 性别: {getGenderText(userInfo.gender)}</span>
                </div>
              </div>
            </div>

            {/* 用户状态信息 */}
            <div className="status-section">
              <div className="status-item">
                <div className="status-label">注册时间</div>
                <div className="status-value">{new Date(userInfo.createTime).toLocaleDateString()}</div>
              </div>
              {userInfo.lastLoginTime && (
                <div className="status-item">
                  <div className="status-label">最后登录</div>
                  <div className="status-value">{new Date(userInfo.lastLoginTime).toLocaleString()}</div>
                </div>
              )}
            </div>
          </Card>
        </div>

        {/* 功能菜单列表 */}
        <div className="menu-section">
          {/* 账户管理 */}
          <div className="menu-group">
            <h3 className="menu-group-title">账户管理</h3>
            <List className="menu-list">
              {menuItems.slice(0, 3).map((item) => (
                <List.Item
                  key={item.id}
                  prefix={
                    <div className="menu-icon-container">
                      {renderIcon(item.icon, { fontSize: '18px', color: item.color })}
                    </div>
                  }
                  arrow
                  onClick={() => handleMenuClick(item)}
                >
                  {item.title}
                </List.Item>
              ))}
            </List>
          </div>

          {/* 设置选项 */}
          <div className="menu-group">
            <h3 className="menu-group-title">设置</h3>
            <List className="menu-list">
              {menuItems.slice(3, 5).map((item) => (
                <List.Item
                  key={item.id}
                  prefix={
                    <div className="menu-icon-container">
                      {renderIcon(item.icon, { fontSize: '18px', color: item.color })}
                    </div>
                  }
                  arrow
                  onClick={() => handleMenuClick(item)}
                >
                  {item.title}
                </List.Item>
              ))}
            </List>
          </div>

          {/* 退出登录 */}
          <div className="menu-group">
            <List className="menu-list exit-list">
              <List.Item
                key={menuItems[5].id}
                prefix={
                  <div className="menu-icon-container exit-icon">
                    {renderIcon(menuItems[5].icon, { fontSize: '18px', color: '#ef4444' })}
                  </div>
                }
                arrow
                onClick={() => handleMenuClick(menuItems[5])}
                className="exit-item"
              >
                <span className="exit-title">{menuItems[5].title}</span>
              </List.Item>
            </List>
          </div>
        </div>

        </div>
      </div>
    </Layout>
  );
};

export default Profile;
