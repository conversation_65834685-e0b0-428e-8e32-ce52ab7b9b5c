.profile-page {
  min-height: 100vh;
  background: #f8fafc;
  position: relative;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Robot<PERSON>, 'Helvetica Neue', Arial, sans-serif;
}

.profile-content {
  padding-bottom: 80px; /* 为底部导航留出空间 */
  padding-top: 16px;
  padding-left: 0;
  padding-right: 0;
}

/* ==========  个人信息模块 ========== */
.profile-header {
  padding: 0;
  margin-bottom: 16px;
}

/* 账户信息卡片 */
.account-card {
  border-radius: 16px;
  border: 1px solid #e2e8f0;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  overflow: hidden;
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
}

.account-card .adm-card-body {
  padding: 20px;
}

/* 账户头部信息 */
.account-header {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 16px;
  border-bottom: 1px solid #f1f5f9;
}

.user-basic-info {
  flex: 1;
  margin-left: 16px;
}

.extension-code {
  font-size: 13px;
  color: #64748b;
  margin-top: 6px;
}

.code-highlight {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  color: white;
  padding: 2px 8px;
  border-radius: 6px;
  font-weight: 600;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  box-shadow: 0 1px 3px rgba(59, 130, 246, 0.3);
}

/* 余额区域 */
.balance-section {
  margin-top: 16px;
}

.total-balance {
  margin-bottom: 16px;
}

.balance-label {
  font-size: 14px;
  color: #64748b;
  margin-bottom: 8px;
  font-weight: 500;
}

.balance-amount {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.amount-value {
  font-size: 32px;
  font-weight: 800;
  color: #1e293b;
  letter-spacing: -0.025em;
  line-height: 1.2;
}

.add-money-btn {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 8px rgba(16, 185, 129, 0.3);
  transition: all 0.2s ease;
  cursor: pointer;
}

.add-money-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(16, 185, 129, 0.4);
}

.balance-details {
  display: flex;
  gap: 12px;
}

.balance-item {
  flex: 1;
  text-align: center;
  padding: 12px 8px;
  background: #f8fafc;
  border-radius: 8px;
  border: 1px solid #e2e8f0;
}

.balance-item.available {
  border-color: rgba(16, 185, 129, 0.2);
  background: rgba(16, 185, 129, 0.05);
}

.balance-item.frozen {
  border-color: rgba(239, 68, 68, 0.2);
  background: rgba(239, 68, 68, 0.05);
}

.balance-item-label {
  font-size: 12px;
  color: #64748b;
  margin-bottom: 4px;
  font-weight: 500;
  text-transform: capitalize;
}

.balance-item-value {
  font-size: 16px;
  font-weight: 700;
  letter-spacing: -0.025em;
}

.available-amount {
  color: #10b981;
}

.frozen-amount {
  color: #ef4444;
}

/* 用户基本信息 */
.user-info {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
  padding-bottom: 16px;
  border-bottom: 1px solid #f1f5f9;
}

.user-meta {
  font-size: 12px;
  color: #64748b;
  margin-top: 4px;
  line-height: 1.4;
}

.user-meta span {
  margin-right: 8px;
}

/* 用户状态信息 */
.status-section {
  display: flex;
  justify-content: space-between;
  gap: 16px;
  margin-top: 16px;
}

.status-item {
  flex: 1;
  text-align: center;
  padding: 12px;
  background: #f8fafc;
  border-radius: 8px;
  border: 1px solid #e2e8f0;
}

.status-label {
  font-size: 12px;
  color: #64748b;
  margin-bottom: 4px;
}

.status-value {
  font-size: 14px;
  color: #1e293b;
  font-weight: 500;
}

/* 加载和错误状态 */
.loading, .error {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
  font-size: 16px;
  color: #64748b;
}

.user-avatar {
  width: 64px;
  height: 64px;
  border-radius: 50%;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  border: 1px solid #e2e8f0;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  overflow: hidden;
}

.avatar-image {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  object-fit: cover;
}

.avatar-icon {
  font-size: 28px;
  color: #64748b;
}

.user-details {
  flex: 1;
}

.username {
  font-size: 22px;
  font-weight: 700;
  color: #1e293b;
  margin: 0 0 6px 0;
  letter-spacing: -0.025em;
}

.user-code {
  font-size: 14px;
  color: #64748b;
  margin: 0;
}

.code-text {
  color: #3b82f6;
  font-weight: 600;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
}

/* 余额信息 */
.balance-section {
  /* 无需额外样式，使用默认布局 */
}

.balance-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 12px;
}

.balance-main {
  flex: 1;
}

.balance-amount {
  font-size: 32px;
  font-weight: 800;
  color: #1e293b;
  letter-spacing: -0.025em;
  line-height: 1.2;
  margin-bottom: 4px;
}

.balance-label {
  font-size: 14px;
  color: #64748b;
  font-weight: 500;
}

.add-btn {
  width: 44px;
  height: 44px;
  border-radius: 50%;
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3);
  transition: all 0.2s ease;
}

.add-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4);
}

.add-btn .anticon {
  font-size: 20px;
  color: white;
}

.balance-details {
  display: flex;
  gap: 12px;
}

.balance-item {
  flex: 1;
  text-align: center;
  padding: 10px 8px;
  background: #f8fafc;
  border-radius: 8px;
  border: 1px solid #e2e8f0;
}

.balance-item-label {
  font-size: 12px;
  color: #64748b;
  margin-bottom: 4px;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.025em;
}

.balance-item-value {
  font-size: 16px;
  font-weight: 700;
  letter-spacing: -0.025em;
}

.balance-item-value.available {
  color: #10b981;
}

.balance-item-value.frozen {
  color: #ef4444;
}

/* ==========  菜单区域 ========== */
.menu-section {
  padding: 0;
}

.menu-group {
  margin-bottom: 16px;
}

.menu-group-title {
  font-size: 16px;
  font-weight: 600;
  color: #1e293b;
  margin: 0 0 8px 0;
  padding: 0;
  letter-spacing: -0.025em;
}

.menu-list {
  background: white;
  border-radius: 12px;
  border: 1px solid #e2e8f0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  overflow: hidden;
  margin: 0;
}

.menu-list .adm-list-item {
  font-size: 15px;
  font-weight: 500;
  color: #1e293b;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.menu-list .adm-list-item:active {
  background-color: rgba(59, 130, 246, 0.05);
}

.menu-icon-container {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background: white;
  border: 1px solid #e2e8f0;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;
}

.menu-icon-container:hover {
  transform: scale(1.05);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

/* 退出登录特殊样式 */
.exit-list {
  border-color: rgba(239, 68, 68, 0.2);
}

.exit-list .adm-list-item:active {
  background-color: rgba(239, 68, 68, 0.05) !important;
}

.exit-title {
  color: #ef4444 !important;
}

.exit-icon {
  border-color: rgba(239, 68, 68, 0.2);
  background: rgba(239, 68, 68, 0.05);
}

/* List组件优化 */
.profile-page .adm-list {
  background: transparent;
}

/* 响应式调整 */
@media (max-width: 375px) {
  .profile-content {
    padding-bottom: 75px;
  }
}

/* 滚动优化 */
.profile-page {
  overflow-x: hidden;
  -webkit-overflow-scrolling: touch;
}

/* 安全区域适配 - Profile页面特殊处理 */
@supports (padding: max(0px)) {
  .profile-page .layout-content {
    padding-left: max(0px, env(safe-area-inset-left));
    padding-right: max(0px, env(safe-area-inset-right));
  }
}

/* 覆盖Layout组件的默认间距 */
.profile-page .layout-content {
  padding-left: 0 !important;
  padding-right: 0 !important;
}

/* 头像占位符样式 */
.avatar-placeholder {
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  border: 2px dashed #cbd5e1;
}

/* 余额卡片动画 */
.balance-card {
  transition: all 0.2s ease;
}

.balance-card:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

/* 菜单项图标动画 */
.menu-icon {
  transition: transform 0.2s ease;
}

.adm-list-item:hover .menu-icon {
  transform: scale(1.1);
}

/* 扩展码高亮样式 */
.extension-code {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  color: white;
  padding: 2px 8px;
  border-radius: 4px;
  font-weight: 600;
  font-size: 12px;
  display: inline-block;
  box-shadow: 0 1px 3px rgba(59, 130, 246, 0.3);
}

/* 余额数字动画 */
.balance-amount {
  transition: color 0.2s ease;
}

/* 退出按钮特殊样式 */
.exit-item {
  color: #ef4444 !important;
}

.exit-item:hover {
  background-color: rgba(239, 68, 68, 0.05) !important;
}

/* 加号按钮脉冲动画 */
@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(59, 130, 246, 0.7);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(59, 130, 246, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(59, 130, 246, 0);
  }
}

.recharge-button {
  animation: pulse 2s infinite;
}

/* 卡片内容布局优化 */
.user-info-section {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.balance-section {
  background: white;
}

/* 文字截断处理 */
.username {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 200px;
}

/* 菜单项间距优化 - 使用默认分割线 */

/* 图标容器统一样式 */
.icon-container {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background: white;
  border: 1px solid #e2e8f0;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;
}

.icon-container:hover {
  transform: scale(1.05);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}
