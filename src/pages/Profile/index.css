/* Profile页面样式 */
.profile-page {
  min-height: 100vh;
  background: #f8fafc;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
}

.profile-content {
  padding-bottom: 80px; /* 为底部导航留出空间 */
  padding-top: 16px;
}

/* ========== 账户信息卡片 ========== */
.profile-header {
  padding: 0;
  margin-bottom: 16px;
}

.account-card {
  border-radius: 16px;
  border: 1px solid #e2e8f0;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  overflow: hidden;
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
}

.account-card .adm-card-body {
  padding: 20px;
}

/* 账户头部信息 */
.account-header {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 16px;
  border-bottom: 1px solid #f1f5f9;
}

.user-avatar {
  width: 64px;
  height: 64px;
  border-radius: 50%;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  border: 1px solid #e2e8f0;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  overflow: hidden;
}

.avatar-image {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  object-fit: cover;
}

.avatar-icon {
  font-size: 28px;
  color: #64748b;
}

.user-basic-info {
  flex: 1;
  margin-left: 16px;
}

.username {
  font-size: 22px;
  font-weight: 700;
  color: #1e293b;
  margin: 0 0 6px 0;
  letter-spacing: -0.025em;
}

.extension-code {
  font-size: 13px;
  color: #64748b;
  margin-top: 6px;
}

.code-highlight {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  color: white;
  padding: 2px 8px;
  border-radius: 6px;
  font-weight: 600;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  box-shadow: 0 1px 3px rgba(59, 130, 246, 0.3);
}

/* 余额区域 */
.balance-section {
  margin-top: 16px;
}

.total-balance {
  margin-bottom: 16px;
}

.balance-label {
  font-size: 14px;
  color: #64748b;
  margin-bottom: 8px;
  font-weight: 500;
}

.balance-amount {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.amount-value {
  font-size: 32px;
  font-weight: 800;
  color: #1e293b;
  letter-spacing: -0.025em;
  line-height: 1.2;
}

.add-money-btn {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 8px rgba(16, 185, 129, 0.3);
  transition: all 0.2s ease;
  cursor: pointer;
}

.add-money-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(16, 185, 129, 0.4);
}

.balance-details {
  display: flex;
  gap: 12px;
}

.balance-item {
  flex: 1;
  text-align: center;
  padding: 12px 8px;
  background: #f8fafc;
  border-radius: 8px;
  border: 1px solid #e2e8f0;
}

.balance-item.available {
  border-color: rgba(16, 185, 129, 0.2);
  background: rgba(16, 185, 129, 0.05);
}

.balance-item.frozen {
  border-color: rgba(239, 68, 68, 0.2);
  background: rgba(239, 68, 68, 0.05);
}

.balance-item-label {
  font-size: 12px;
  color: #64748b;
  margin-bottom: 4px;
  font-weight: 500;
  text-transform: capitalize;
}

.balance-item-value {
  font-size: 16px;
  font-weight: 700;
  letter-spacing: -0.025em;
}

.available-amount {
  color: #10b981;
}

.frozen-amount {
  color: #ef4444;
}

/* ========== 功能菜单区域 ========== */
.menu-section {
  padding: 0;
}

.menu-card {
  border-radius: 16px;
  border: 1px solid #e2e8f0;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  overflow: hidden;
}

.menu-card .adm-card-body {
  padding: 0;
}

.function-menu {
  background: transparent;
}

.function-menu .adm-list-item {
  padding: 16px 20px;
  cursor: pointer;
  transition: background-color 0.2s ease;
  border-bottom: 1px solid #f1f5f9;
}

.function-menu .adm-list-item:last-child {
  border-bottom: none;
}

.function-menu .adm-list-item:active {
  background-color: rgba(59, 130, 246, 0.05);
}

/* 禁用默认箭头 */
.function-menu .adm-list-item .adm-list-item-content-arrow {
  display: none !important;
}

.menu-icon-wrapper {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.8);
  border: 1px solid #e2e8f0;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;
}

.menu-content {
  flex: 1;
  margin-left: 16px;
}

.menu-title {
  font-size: 16px;
  font-weight: 600;
  color: #1e293b;
  letter-spacing: -0.025em;
}

/* 自定义箭头样式 */
.function-menu .adm-list-item-content-extra .anticon {
  color: #999 !important;
  font-size: 14px !important;
  transition: color 0.2s ease;
}

.function-menu .adm-list-item:hover .adm-list-item-content-extra .anticon {
  color: #666 !important;
}

/* 退出登录特殊样式 */
.exit-item {
  border-top: 1px solid rgba(239, 68, 68, 0.1) !important;
  background: rgba(239, 68, 68, 0.02) !important;
}

.exit-item:active {
  background-color: rgba(239, 68, 68, 0.08) !important;
}

.exit-item .menu-title {
  color: #ef4444 !important;
}

.exit-item .menu-icon-wrapper {
  border-color: rgba(239, 68, 68, 0.2);
  background: rgba(239, 68, 68, 0.05);
}

.exit-item .adm-list-item-content-extra .anticon {
  color: rgba(239, 68, 68, 0.7) !important;
}

/* ========== 加载和错误状态 ========== */
.loading, .error {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
  font-size: 16px;
  color: #64748b;
}

/* ========== 响应式优化 ========== */
@media (max-width: 375px) {
  .profile-content {
    padding-bottom: 75px;
  }
}

/* 覆盖Layout组件的默认间距 */
.profile-page .layout-content {
  padding-left: 0 !important;
  padding-right: 0 !important;
}

/* 安全区域适配 */
@supports (padding: max(0px)) {
  .profile-page .layout-content {
    padding-left: max(0px, env(safe-area-inset-left));
    padding-right: max(0px, env(safe-area-inset-right));
  }
}
