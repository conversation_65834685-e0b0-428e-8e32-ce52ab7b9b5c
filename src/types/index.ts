// 轮播图数据类型
export interface BannerItem {
  id: number;
  image: string;
  title?: string;
  link?: string;
}

// 收益统计数据类型
export interface EarningsData {
  todayEarnings: number;
  todayCommission: number;
  totalRevenue: number;
  myBalance: number;
}

// 功能快捷入口数据类型
export interface QuickAction {
  id: number;
  title: string;
  icon: string;
  path: string;
  color?: string;
}

// 收益展示数据类型
export interface RevenueItem {
  id: number;
  account: string;
  amount: number;
  description: string;
  time: string;
}

// 合作品牌数据类型
export interface Partner {
  id: number;
  name: string;
  logo: string;
  link?: string;
}

// 底部导航数据类型
export interface TabItem {
  key: string;
  title: string;
  icon: string;
  path: string;
}

// API响应基础类型
export interface ApiResponse<T = any> {
  code: number;
  msg: string;
  data: T;
}

// 分页数据类型
export interface PageData<T = any> {
  pageNum: number;
  pageSize: number;
  total: number;
  list: T[];
}
