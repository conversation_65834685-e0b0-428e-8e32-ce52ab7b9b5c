import {get, post, put} from '../utils/request';

// 登录接口参数
export interface LoginParams {
  loginType: 'username' | 'phone';
  loginValue: string;
  password: string;
}

// 注册接口参数
export interface RegisterParams {
  username: string;
  password: string;
  nickname: string;
  phone?: string;
  email?: string;
}

// 修改密码参数
export interface ChangePasswordParams {
  oldPassword: string;
  newPassword: string;
}

// 设置支付密码参数
export interface SetPayPasswordParams {
  payPassword: string;
}

// 修改支付密码参数
export interface ChangePayPasswordParams {
  oldPayPassword: string;
  newPayPassword: string;
}

// 更新用户信息参数
export interface UpdateUserInfoParams {
  nickname?: string;
  email?: string;
  avatar?: string;
  gender?: number;
  birthday?: string;
  bio?: string;
}

// 用户信息
export interface UserInfo {
  id: number;
  username: string;
  nickname: string;
  email?: string;
  phone?: string;
  avatar?: string;
  gender?: number;
  birthday?: string;
  bio?: string;
  status: number;
  inviteCode: string;
  lastLoginTime?: string;
  lastLoginIp?: string;
  createTime: string;
  updateTime: string;
}

// API响应格式
export interface ApiResponse<T = any> {
  code: number;
  message: string;
  data: T;
  timestamp: number;
}

// 登录响应
export interface LoginResponse {
  token: string;
  user: UserInfo;
}

// 登录
export async function login(params: LoginParams): Promise<ApiResponse<LoginResponse>> {
  return post('/user-api/auth/login', params);
}

// 注册
export async function register(params: RegisterParams): Promise<ApiResponse<string>> {
  return post('/user-api/auth/register', params);
}

// 登出
export async function logout(): Promise<ApiResponse<string>> {
  return post('/user-api/auth/logout');
}

// 获取当前用户信息
export async function getUserInfo(): Promise<ApiResponse<UserInfo>> {
  return get('/user-api/auth/userinfo');
}

// 修改密码
export async function changePassword(params: ChangePasswordParams): Promise<ApiResponse<void>> {
  return post('/user-api/auth/changePassword', params);
}

// 设置支付密码
export async function setPayPassword(params: SetPayPasswordParams): Promise<ApiResponse<void>> {
  return post('/user-api/auth/setPayPassword', params);
}

// 修改支付密码
export async function changePayPassword(params: ChangePayPasswordParams): Promise<ApiResponse<void>> {
  return post('/user-api/auth/changePayPassword', params);
}

// 更新用户信息
export async function updateUserInfo(params: UpdateUserInfoParams): Promise<ApiResponse<void>> {
  return put('/user-api/auth/updateUserInfo', params);
}
