2025-05-26 02:18:13 [main] INFO  com.sd.user.UserApplication - Starting UserApplication using Java 21.0.6 with PID 9801 (/Users/<USER>/workspace/project/sd/backend-user/target/classes started by garen in /Users/<USER>/workspace/project/sd)
2025-05-26 02:18:13 [main] INFO  com.sd.user.UserApplication - No active profile set, falling back to 1 default profile: "default"
2025-05-26 02:18:13 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-05-26 02:18:13 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-05-26 02:18:13 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 6 ms. Found 0 Redis repository interfaces.
2025-05-26 02:18:13 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port 18081 (http)
2025-05-26 02:18:13 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-05-26 02:18:13 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.16]
2025-05-26 02:18:13 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/user-api] - Initializing Spring embedded WebApplicationContext
2025-05-26 02:18:13 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 683 ms
2025-05-26 02:18:13 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-05-26 02:18:14 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-05-26 02:18:14 [main] INFO  c.b.m.e.spring.MybatisPlusApplicationContextAware - Register ApplicationContext instances org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@*************-05-26 02:18:15 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port 18081 (http) with context path '/user-api'
2025-05-26 02:18:15 [main] INFO  com.sd.user.UserApplication - Started UserApplication in 2.105 seconds (process running for 7.603)
2025-05-26 02:19:06 [http-nio-18081-exec-1] INFO  o.a.c.c.C.[Tomcat].[localhost].[/user-api] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-05-26 02:19:06 [http-nio-18081-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-05-26 02:19:06 [http-nio-18081-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-05-26 02:22:26 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closing ...
2025-05-26 02:22:26 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
