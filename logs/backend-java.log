2025-05-25 02:35:40 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closing ...
2025-05-25 02:35:40 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-05-25 02:35:55 [main] INFO  com.sd.admin.BackendApplication - Starting BackendApplication using Java 21.0.6 with PID 2308 (/Users/<USER>/workspace/project/sd/backend-admin/target/classes started by garen in /Users/<USER>/workspace/project/sd)
2025-05-25 02:35:55 [main] INFO  com.sd.admin.BackendApplication - No active profile set, falling back to 1 default profile: "default"
2025-05-25 02:35:55 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-05-25 02:35:55 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-05-25 02:35:55 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 12 ms. Found 0 Redis repository interfaces.
2025-05-25 02:35:56 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port 8080 (http)
2025-05-25 02:35:56 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-05-25 02:35:56 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.16]
2025-05-25 02:35:56 [main] INFO  o.a.c.c.ContainerBase.[Tomcat].[localhost].[/api] - Initializing Spring embedded WebApplicationContext
2025-05-25 02:35:56 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 819 ms
2025-05-25 02:35:56 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-05-25 02:35:56 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-05-25 02:35:57 [main] INFO  c.b.m.e.spring.MybatisPlusApplicationContextAware - Register ApplicationContext instances org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@473b3b7a
2025-05-25 02:35:57 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port 8080 (http) with context path '/api'
2025-05-25 02:35:57 [main] INFO  com.sd.admin.BackendApplication - Started BackendApplication in 2.613 seconds (process running for 8.198)
