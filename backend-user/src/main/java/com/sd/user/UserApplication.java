package com.sd.user;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;

/**
 * 用户端应用启动类
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
@SpringBootApplication(scanBasePackages = {"com.sd.user", "com.sd.common"})
@MapperScan("com.sd.user.mapper")
public class UserApplication {

    public static void main(String[] args) {
        SpringApplication.run(UserApplication.class, args);
        System.out.println("(♥◠‿◠)ﾉﾞ  用户端服务启动成功   ლ(´ڡ`ლ)ﾞ");
    }
}
