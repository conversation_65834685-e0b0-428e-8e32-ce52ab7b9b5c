package com.sd.common.api;

import lombok.Data;

import java.io.Serializable;

/**
 * 分页请求类
 *
 * <AUTHOR>
 */
@Data
public class PageRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 当前页码
     */
    private int current = 1;

    /**
     * 每页大小
     */
    private int pageSize = 10;

    /**
     * 排序字段
     */
    private String sorter;

    /**
     * 是否查询总数
     */
    private boolean searchCount = true;

    /**
     * 获取开始位置
     *
     * @return 开始位置
     */
    public int getOffset() {
        return (current - 1) * pageSize;
    }
}
